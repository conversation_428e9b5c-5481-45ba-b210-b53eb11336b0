            about-appcove {
                div{
                    position: relative;
                    padding: 1rem;
                    background-color: bisque;
                
                }
                #page-heading {
                    margin-top: 2rem;

                    h1 {
                        font-size: 50px;
                        color: #2C4861;
                        margin-bottom: 1rem;
                        line-height: 1.6;
                        font-style: Ralewayregular;
                    }

                    p {
                        font-size: 2rem;
                        color: #00bfa5;
                        margin-bottom: 1rem;
                        line-height: 1.6;
                        font-weight: 600;
                        font-style: Open Sans;
                    }
                    #circle-image {
                        height: 300px;
                        width: auto;
                        top: 200px;
                        left: 200px;
                        max-width: 350px;
                        height: auto;
                        margin-right: 10px;
                        margin-bottom: 0px;
                        float: left;
                    }

                    strong {
                        color: #012650f8;
                        font-weight: bold;
                    }
                }
                #page-body {
                    margin-top: 1rem; 
                    header {
                        p {
                            font-size: 1.5rem;
                            color: #21536C;
                            margin-bottom: 1rem;
                            line-height: 1.6;
                            font-weight: bold;
                            font-style: Open Sans;
                        }
                    }
                }

                #page-body {
                    margin-top: 1rem; 

                        p {
                            font-size: 1rem;
                            color: #000000;
                            margin-bottom: 1rem;
                            line-height: 1.6;
                            font-weight: bold;
                            font-style: Open Sans;
                        }
                        strong {
                            color: #012650f8;
                            font-weight: bold;
                        }
                        br {
                            margin-bottom: 0.8rem;
                        }
                        content {
                        font-size: 1.2rem;
                        color: #4A4A4A;
                        line-height: 1.6;
                        font-style: Open Sans;
                        }
                    }

                #page-footer {
                    margin-top: 2rem;

                    p {
                        margin-bottom: 0.8rem;
                        font-size: 1.2rem;
                        color: #000000;
                        line-height: 1.6;
                        font-weight: bold;
                        font-style: Open Sans regular;
                    }
                    strong {
                        color: #21536C;
                        font-weight: bold;
                        font-style: Open Sans bold;
                    }
                    
                    br {
                        margin-bottom: 0.8rem;
                    }    
                }

                #page-main {
                    margin-top: 2rem;
                    display: flex;
                    flex-direction: column;
                    gap: 2rem;

                    header-2 {
                        font-size: 2rem;
                        color: #012650f8;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    p {
                        font-size: 1rem;
                        color: #4A4A4A;
                        line-height: 1.6;
                    }
                    br {
                        margin-bottom: 0.5rem;
                    }
                    header-3 {
                        font-size: 2rem;
                        color: #012650f8;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    hr {
                        all: initial;
                        display: block;
                        border-bottom: 3.2px dotted rgb(9, 81, 129);
                    }   
                    .arrow-icon-wrapper {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 3rem;
                        height: 3rem;
                        border-radius: 80%;
                        background-color: #007b8a;
                        margin-top: 2rem;
                        margin-bottom: 3rem;
                        cursor: pointer;
                        transition: all 0.3s ease;

                    &:hover {
                        background-color: #0887a7;
                    }
                    .arrow-icon {
                        color: white;
                        font-size: 2rem;
                        transition: all 0.3s ease;
                    }
                }
            }

                #page-body {
                    margin-bottom: 1rem;
                    margin-top: 1rem;

                    h6 {
                        font-size: 2rem;
                        color: #009688;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                }
                .staff-grid {
                    display: grid;
                    grid-template-columns: repeat(2, 350px);
                    gap: 20px 25px; /* row-gap column-gap */

                }
                .staff-card {
                    background-color: #F5F0E9;
                    border-radius: 12px;
                    width: 350px;
                    padding: 20px;
                    margin-bottom: 0;
                    height: 120px;
                    flex-direction: inherit;
                    justify-content: center;
                h4 {
                        font-size: 1.3rem;
                        color: #000000;
                        font-weight: bold;
                        font-style: open sans;
                }
                p   {
                        font-size: 1rem;
                        color: #333333;
                        
                    }
                years {
                        font-size: 1rem;
                        color:  #009688;
                        font-weight: bold;
                        font-style: open sans;
                    }
                }

                #page-footer {
                    margin-top: 2rem;
                    margin-bottom: 2rem;

                    p {
                        font-size: 1.2rem;
                        color: #1f2527;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    strong {
                        font-weight: bold;
                    }
                }

                #page-body {
                    margin-top: 2rem;
                    margin-bottom: 2rem;

                    p {
                        font-size: 1.2rem;
                        color: #1f2527;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    strong  {
                        font-weight: bold;
                    }
                    br {
                        margin-bottom: 0.8rem;
                        margin-top: 0.8rem;
                    }
                }

                #timeline-panel {
                        background-color: #e8ebe0a8;
                        border-radius: 0.5rem;
                        padding: 1rem 1.25rem;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                        width: 550px;
                    
                    h5 {
                        font-weight: bold;
                        color: #21536C;
                        font-size: 2rem;
                        line-height: 1;
                    }
                    p.notable{
                        font-size: 1rem;
                        color: #05475e;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    .timeline-container {
                        position: relative;
                        margin-left: 10px;
                        border-left: 2px dotted #129686;
                        padding: 20px 0;
                        margin-top: 1rem;
                        margin-bottom: 1rem;
                        max-width: 1000px;
                    }
                    .timeline-item {
                        position: relative;
                        padding-left: 1rem;
                        margin-bottom: 2rem;
                    }
                    .timeline-dot {
                        height: 16px;
                        width: 16px;
                        background-color: #119191;
                        border-radius: 50%;
                        display: inline-block;
                        margin-right: 8px;
                        position: absolute;
                        left: -7px;
                        top: 3px;
                    }
                    .timeline-year {
                        font-weight: bold;
                        color: #054f5a;
                    }
                    .active-project {
                        font-weight: 600;
                        color: #119191;
                        font-style: italic;
                    }
                    .inactive-project {
                        font-weight: 600;
                        color: #292c2e;
                        font-style: italic;
                    }
                    strong {
                        color: #01070ff8;
                        font-weight: bold;
                        font-size: 1.3rem;
                    }
                    small {
                        color:#4A4A4A ;
                        font-weight: bold;
                        font-size: 1.2rem;
                    }
                }
                    br {
                        margin-bottom: 0.8rem;
                        margin-top: 0.8rem;
                    }
                    .company-logo {
                        justify-content: left;
                        align-items: left;
                        margin-top: 2rem;
                        
                    img {
                        height: 50px;
                        width: auto;
                    }
                }
            }
        
        
            
        
    
              
